"""
Pure Python coverage file management.

This module handles coverage file collection, merging, and report generation
without relying on CMake targets or shell scripts.
"""

import shutil
import subprocess
from pathlib import Path
from typing import List, Optional, Tuple

from pydcov.utils.coverage_tools import CoverageToolManager
from pydcov.utils.logging_config import get_logger


class CoverageFileManager:
    """Manages coverage file operations using pure Python."""
    
    def __init__(self, build_dir: Path, coverage_dir: Path):
        self.build_dir = Path(build_dir)
        self.coverage_dir = Path(coverage_dir)
        self.incremental_dir = self.coverage_dir / 'incremental'
        self.logger = get_logger()
        self.tool_manager = CoverageToolManager()
    
    def init_incremental(self) -> bool:
        """
        Initialize incremental coverage collection.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            # Remove existing incremental directory
            if self.incremental_dir.exists():
                shutil.rmtree(self.incremental_dir)
            
            # Create fresh incremental directory
            self.incremental_dir.mkdir(parents=True, exist_ok=True)
            
            self.logger.info(f"Incremental coverage initialized at {self.incremental_dir}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize incremental coverage: {e}")
            return False
    
    def collect_coverage_files(self) -> Tuple[int, int]:
        """
        Collect coverage files from build directory to incremental directory.
        
        Returns:
            Tuple of (profraw_count, gcda_count)
        """
        self.incremental_dir.mkdir(parents=True, exist_ok=True)
        
        profraw_count = 0
        gcda_count = 0
        
        try:
            # Collect .profraw files (Clang)
            profraw_files = list(self.build_dir.rglob('*.profraw'))
            # Exclude files already in incremental directory
            profraw_files = [f for f in profraw_files if not str(f).startswith(str(self.incremental_dir))]
            
            for profraw_file in profraw_files:
                try:
                    dest = self.incremental_dir / profraw_file.name
                    shutil.copy2(profraw_file, dest)
                    profraw_count += 1
                except Exception as e:
                    self.logger.warning(f"Failed to copy {profraw_file}: {e}")
            
            # Collect .gcda files (GCC)
            gcda_files = list(self.build_dir.rglob('*.gcda'))
            gcda_files = [f for f in gcda_files if not str(f).startswith(str(self.incremental_dir))]
            
            for gcda_file in gcda_files:
                try:
                    dest = self.incremental_dir / gcda_file.name
                    shutil.copy2(gcda_file, dest)
                    gcda_count += 1
                except Exception as e:
                    self.logger.warning(f"Failed to copy {gcda_file}: {e}")
            
            # Also collect .gcno files for GCC
            gcno_files = list(self.build_dir.rglob('*.gcno'))
            gcno_files = [f for f in gcno_files if not str(f).startswith(str(self.incremental_dir))]
            
            for gcno_file in gcno_files:
                try:
                    dest = self.incremental_dir / gcno_file.name
                    shutil.copy2(gcno_file, dest)
                except Exception as e:
                    self.logger.warning(f"Failed to copy {gcno_file}: {e}")
            
            self.logger.info(f"Collected {profraw_count} .profraw files and {gcda_count} .gcda files")
            return profraw_count, gcda_count
            
        except Exception as e:
            self.logger.error(f"Failed to collect coverage files: {e}")
            return 0, 0
    
    def merge_coverage_data(self, compiler: str = None) -> bool:
        """
        Merge collected coverage data.
        
        Args:
            compiler: Compiler type ('clang' or 'gcc')
            
        Returns:
            True if successful, False otherwise
        """
        if compiler is None:
            compiler = self.tool_manager.detect_compiler()
        
        self.coverage_dir.mkdir(parents=True, exist_ok=True)
        
        if compiler == 'clang':
            return self._merge_clang_data()
        elif compiler == 'gcc':
            return self._merge_gcc_data()
        else:
            self.logger.error(f"Unsupported compiler: {compiler}")
            return False
    
    def _merge_clang_data(self) -> bool:
        """Merge Clang coverage data using llvm-profdata."""
        tools = self.tool_manager.get_coverage_tools('clang')
        llvm_profdata = tools.get('llvm_profdata')
        
        if not llvm_profdata:
            self.logger.error("llvm-profdata not found")
            return False
        
        # Find .profraw files in incremental directory
        profraw_files = list(self.incremental_dir.glob('*.profraw'))
        if not profraw_files:
            self.logger.warning("No .profraw files found to merge")
            return False
        
        output_file = self.coverage_dir / 'incremental_merged.profdata'
        
        try:
            cmd = [llvm_profdata, 'merge', '-sparse'] + [str(f) for f in profraw_files] + ['-o', str(output_file)]
            
            self.logger.info(f"Merging {len(profraw_files)} .profraw files...")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                self.logger.success(f"Successfully merged coverage data to {output_file}")
                return True
            else:
                self.logger.error(f"llvm-profdata merge failed: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            self.logger.error("llvm-profdata merge timed out")
            return False
        except Exception as e:
            self.logger.error(f"Failed to merge Clang coverage data: {e}")
            return False
    
    def _merge_gcc_data(self) -> bool:
        """Merge GCC coverage data using lcov."""
        tools = self.tool_manager.get_coverage_tools('gcc')
        lcov = tools.get('lcov')
        
        if not lcov:
            self.logger.error("lcov not found")
            return False
        
        # Check for .gcda files
        gcda_files = list(self.incremental_dir.glob('*.gcda'))
        if not gcda_files:
            self.logger.warning("No .gcda files found to merge")
            return False
        
        output_file = self.coverage_dir / 'incremental_merged.info'
        
        try:
            cmd = [
                lcov, '--capture',
                '--directory', str(self.incremental_dir),
                '--output-file', str(output_file),
                '--rc', 'branch_coverage=1',
                '--ignore-errors', 'gcov,source,unused'
            ]
            
            self.logger.info(f"Generating coverage info from {len(gcda_files)} .gcda files...")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                self.logger.success(f"Successfully generated coverage info at {output_file}")
                return True
            else:
                self.logger.error(f"lcov failed: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            self.logger.error("lcov timed out")
            return False
        except Exception as e:
            self.logger.error(f"Failed to merge GCC coverage data: {e}")
            return False
    
    def generate_report(self, compiler: str = None, executables: List[Path] = None) -> bool:
        """
        Generate coverage report.
        
        Args:
            compiler: Compiler type
            executables: List of executable paths for Clang coverage
            
        Returns:
            True if successful, False otherwise
        """
        if compiler is None:
            compiler = self.tool_manager.detect_compiler()
        
        report_dir = self.coverage_dir / 'incremental_report'
        report_dir.mkdir(parents=True, exist_ok=True)
        
        if compiler == 'clang':
            return self._generate_clang_report(report_dir, executables)
        elif compiler == 'gcc':
            return self._generate_gcc_report(report_dir)
        else:
            self.logger.error(f"Unsupported compiler: {compiler}")
            return False
    
    def _generate_clang_report(self, report_dir: Path, executables: List[Path] = None) -> bool:
        """Generate Clang coverage report using llvm-cov."""
        tools = self.tool_manager.get_coverage_tools('clang')
        llvm_cov = tools.get('llvm_cov')
        
        if not llvm_cov:
            self.logger.error("llvm-cov not found")
            return False
        
        profdata_file = self.coverage_dir / 'incremental_merged.profdata'
        if not profdata_file.exists():
            self.logger.error(f"Merged profdata file not found: {profdata_file}")
            return False
        
        if not executables:
            # Try to find executables automatically
            executables = list(self.build_dir.rglob('*'))
            executables = [e for e in executables if e.is_file() and e.stat().st_mode & 0o111]
        
        if not executables:
            self.logger.warning("No executables found for coverage report")
            return False
        
        try:
            # Generate HTML report
            cmd = [llvm_cov, 'show'] + [str(e) for e in executables] + [
                f'-instr-profile={profdata_file}',
                '-format=html',
                f'-output-dir={report_dir}'
            ]
            
            self.logger.info("Generating HTML coverage report...")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=180)
            
            if result.returncode == 0:
                self.logger.success(f"HTML report generated at {report_dir / 'index.html'}")
                return True
            else:
                self.logger.error(f"llvm-cov show failed: {result.stderr}")
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to generate Clang report: {e}")
            return False
    
    def _generate_gcc_report(self, report_dir: Path) -> bool:
        """Generate GCC coverage report using genhtml."""
        tools = self.tool_manager.get_coverage_tools('gcc')
        genhtml = tools.get('genhtml')
        
        if not genhtml:
            self.logger.error("genhtml not found")
            return False
        
        info_file = self.coverage_dir / 'incremental_merged.info'
        if not info_file.exists():
            self.logger.error(f"Merged info file not found: {info_file}")
            return False
        
        try:
            cmd = [
                genhtml, str(info_file),
                '--output-directory', str(report_dir),
                '--rc', 'branch_coverage=1',
                '--ignore-errors', 'source,unused'
            ]
            
            self.logger.info("Generating HTML coverage report...")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=180)
            
            if result.returncode == 0:
                self.logger.success(f"HTML report generated at {report_dir / 'index.html'}")
                return True
            else:
                self.logger.error(f"genhtml failed: {result.stderr}")
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to generate GCC report: {e}")
            return False
    
    def get_status(self) -> dict:
        """Get current status of incremental coverage."""
        profraw_files = list(self.incremental_dir.glob('*.profraw')) if self.incremental_dir.exists() else []
        gcda_files = list(self.incremental_dir.glob('*.gcda')) if self.incremental_dir.exists() else []
        
        merged_profdata = self.coverage_dir / 'incremental_merged.profdata'
        merged_info = self.coverage_dir / 'incremental_merged.info'
        report_dir = self.coverage_dir / 'incremental_report'
        
        return {
            'incremental_dir_exists': self.incremental_dir.exists(),
            'profraw_count': len(profraw_files),
            'gcda_count': len(gcda_files),
            'merged_profdata_exists': merged_profdata.exists(),
            'merged_info_exists': merged_info.exists(),
            'report_exists': report_dir.exists() and (report_dir / 'index.html').exists(),
            'compiler': self.tool_manager.detect_compiler()
        }
