name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    name: Test on ${{ matrix.os }} with ${{ matrix.compiler }}
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, macos-latest]
        compiler: [gcc, clang]
        exclude:
          # macOS doesn't have gcc in the default environment
          - os: macos-latest
            compiler: gcc

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install system dependencies (Ubuntu)
      if: matrix.os == 'ubuntu-latest'
      run: |
        sudo apt-get update
        sudo apt-get install -y \
          build-essential \
          gcc \
          g++ \
          clang \
          llvm \
          cmake \
          lcov \
          python3-pip

    - name: Install system dependencies (macOS)
      if: matrix.os == 'macos-latest'
      run: |
        # Xcode command line tools are pre-installed on GitHub Actions macOS runners
        # Install Homebrew packages
        brew install llvm lcov

    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pytest pytest-cov pytest-xdist pytest-html coverage build wheel

    - name: Set up environment variables
      run: |
        if [ "${{ matrix.compiler }}" = "clang" ]; then
          if [ "${{ matrix.os }}" = "macos-latest" ]; then
            echo "PATH=/opt/homebrew/opt/llvm/bin:$PATH" >> $GITHUB_ENV
          elif [ "${{ matrix.os }}" = "ubuntu-latest" ]; then
            # On Ubuntu, LLVM tools might be versioned (e.g., llvm-profdata-14)
            # Try to find the correct version and create symlinks if needed
            LLVM_VERSION=$(clang --version | grep -oP 'clang version \K[0-9]+' | head -1)
            if [ -n "$LLVM_VERSION" ]; then
              echo "LLVM_VERSION=$LLVM_VERSION" >> $GITHUB_ENV
            fi
          fi
          echo "CC=clang" >> $GITHUB_ENV
          echo "CXX=clang++" >> $GITHUB_ENV
        else
          echo "CC=gcc" >> $GITHUB_ENV
          echo "CXX=g++" >> $GITHUB_ENV
        fi

        # Set coverage environment for Clang
        if [ "${{ matrix.compiler }}" = "clang" ]; then
          echo "LLVM_PROFILE_FILE=build/coverage-%p.profraw" >> $GITHUB_ENV
        fi

    - name: Build project
      run: |
        mkdir -p build
        cd build
        cmake .. -DCMAKE_BUILD_TYPE=Release
        make

    - name: Test PyDCov package installation
      run: |
        # Build and install PyDCov package
        python -m build --wheel
        pip install dist/*.whl

        # Verify installation
        pydcov --version
        python -c "import pydcov; print('PyDCov package imported successfully')"

    - name: Run PyDCov package tests
      run: |
        # Test package functionality
        python -m pytest tests/test_package_installation.py -v --tb=short
        python -m pytest tests/test_cli_commands.py -v --tb=short
        python -m pytest tests/test_template_system.py -v --tb=short

    - name: Run integration tests
      run: |
        # Test integration scenarios (marked as slow)
        python -m pytest tests/test_integration.py -v --tb=short --run-slow

    - name: Run example module tests
      run: |
        # Test the example C/C++ modules
        python -m pytest examples/algorithm/tests/ examples/statistics/tests/ -v --tb=short

    - name: Build with coverage
      run: |
        rm -rf build
        mkdir -p build
        cd build
        # Force specific compiler for coverage
        if [ "${{ matrix.compiler }}" = "gcc" ]; then
          CC=gcc CXX=g++ cmake .. -DENABLE_COVERAGE=ON -DCMAKE_BUILD_TYPE=Debug
        else
          CC=clang CXX=clang++ cmake .. -DENABLE_COVERAGE=ON -DCMAKE_BUILD_TYPE=Debug
        fi
        make

    - name: Test template system with coverage build
      run: |
        # Test that generated templates work with coverage
        pydcov init-template coverage_test_project --template basic_cpp
        cd coverage_test_project
        mkdir build && cd build

        # Try to configure with coverage (might fail due to missing tools)
        if cmake .. -DENABLE_COVERAGE=ON -DCMAKE_BUILD_TYPE=Debug; then
          make
          make test
          echo "Coverage build successful"
        else
          echo "Coverage build failed (likely due to missing coverage tools)"
          # Try regular build instead
          cmake .. -DCMAKE_BUILD_TYPE=Release
          make
          make test
        fi
        cd ../..

    - name: Run tests with coverage
      run: |
        # Ensure the executable path is correct for tests
        export PYDCOV_EXECUTABLE="./build/pydcov"
        python -m pytest examples/algorithm/tests/ examples/statistics/tests/ -v --tb=short

    - name: Generate coverage report
      run: |
        cd build
        make coverage-report

    - name: Upload coverage to Codecov
      if: matrix.os == 'ubuntu-latest' && matrix.compiler == 'gcc'
      uses: codecov/codecov-action@v4
      with:
        file: build/coverage/coverage.info
        flags: unittests
        name: codecov-umbrella
        fail_ci_if_error: false

    - name: Upload coverage artifacts
      uses: actions/upload-artifact@v4
      with:
        name: coverage-${{ matrix.os }}-${{ matrix.compiler }}
        path: |
          build/coverage/
          !build/coverage/*.profraw
        retention-days: 7

  package-test:
    name: Test PyDCov Package Distribution
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ['3.11', '3.12', '3.13']

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}

    - name: Install build dependencies
      run: |
        python -m pip install --upgrade pip
        pip install build wheel pytest

    - name: Build wheel
      run: |
        python -m build --wheel
        ls -la dist/

    - name: Test wheel installation
      run: |
        # Install from wheel
        pip install dist/*.whl

        # Test basic functionality
        pydcov --version
        pydcov --help

        # Test imports
        python -c "import pydcov; print('Package imported successfully')"
        python -c "from pydcov import CoverageManager, IncrementalCoverageManager; print('Core classes imported')"

    - name: Test template creation
      run: |
        # Test template system
        pydcov init-template test_wheel_project --template basic_cpp
        ls -la test_wheel_project/

        # Verify template structure
        test -f test_wheel_project/CMakeLists.txt
        test -f test_wheel_project/README.md
        test -d test_wheel_project/src
        test -d test_wheel_project/tests
        test -d test_wheel_project/cmake

    - name: Test CMake integration
      run: |
        mkdir test_cmake_integration
        pydcov init-cmake --project-root test_cmake_integration
        test -f test_cmake_integration/cmake/coverage.cmake
        test -f test_cmake_integration/cmake/COVERAGE_USAGE.md

    - name: Upload wheel artifact
      uses: actions/upload-artifact@v4
      with:
        name: pydcov-wheel-python${{ matrix.python-version }}
        path: dist/*.whl
        retention-days: 7

  package:
    name: Package Deliverable Files
    runs-on: ubuntu-latest
    needs: [test, package-test]

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        pip install build wheel

    - name: Build PyDCov wheel
      run: |
        python -m build --wheel

    - name: Create deliverable package
      run: |
        mkdir -p dist/pydcov-package

        # Copy PyDCov wheel
        cp dist/*.whl dist/pydcov-package/

        # Copy core deliverable files
        cp -r pydcov/ dist/pydcov-package/
        cp -r cmake/ dist/pydcov-package/
        cp -r docs/ dist/pydcov-package/

        # Copy root configuration files
        cp CMakeLists.txt dist/pydcov-package/
        cp README.md dist/pydcov-package/
        cp LICENSE dist/pydcov-package/
        cp requirements.txt dist/pydcov-package/
        cp pytest.ini dist/pydcov-package/
        cp EXAMPLES.md dist/pydcov-package/
        cp CMAKE_TECHNICAL_DOCUMENTATION.md dist/pydcov-package/
        cp pyproject.toml dist/pydcov-package/
        cp MANIFEST.in dist/pydcov-package/

        # Copy example modules (for demonstration)
        cp -r examples/ dist/pydcov-package/

        # Clean up build artifacts from examples (keep tests for reference)
        find dist/pydcov-package/examples/ -name "build" -type d -exec rm -rf {} + 2>/dev/null || true
        find dist/pydcov-package/ -name "*.profraw" -delete
        find dist/pydcov-package/ -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
        find dist/pydcov-package/ -name "*.pyc" -delete

    - name: Create package archive
      run: |
        cd dist
        tar -czf pydcov-package.tar.gz pydcov-package/
        zip -r pydcov-package.zip pydcov-package/

    - name: Upload deliverable package
      uses: actions/upload-artifact@v4
      with:
        name: pydcov-deliverable-package
        path: |
          dist/pydcov-package.tar.gz
          dist/pydcov-package.zip
        retention-days: 30

  release:
    name: Create Release
    runs-on: ubuntu-latest
    needs: [test, package]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Download deliverable package
      uses: actions/download-artifact@v4
      with:
        name: pydcov-deliverable-package
        path: dist/

    - name: Upload release artifacts
      uses: actions/upload-artifact@v4
      with:
        name: release-package
        path: dist/
        retention-days: 90
