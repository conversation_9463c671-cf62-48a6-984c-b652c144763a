[tool:pytest]
# Pytest configuration for pydcov project
testpaths = tests examples/algorithm/tests examples/statistics/tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Minimum version
minversion = 6.0

# Add options
addopts = 
    -v
    --tb=short
    --strict-markers
    --strict-config
    --disable-warnings

# Test discovery patterns
norecursedirs = 
    .git
    .tox
    dist
    build
    *.egg
    __pycache__
    .pytest_cache
    venv
    .venv

# Markers (defined in conftest.py as well for documentation)
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    statistical: marks tests that verify statistical calculations
    algorithm: marks tests for the algorithm module
    statistics: marks tests for the statistics module
    coverage_tools: marks tests for the coverage tools
    package: marks tests for the PyDCov package functionality
    cli: marks tests for the CLI interface
    template: marks tests for the template system

# Logging
log_cli = false
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Coverage settings (if using pytest-cov)
# These are optional and can be enabled if needed
# addopts = --cov=coverage_tools --cov-report=html --cov-report=term-missing
