LICENSE
MANIFEST.in
README_PACKAGE.md
pyproject.toml
pydcov/__init__.py
pydcov/cli.py
pydcov/cmake/COVERAGE_USAGE.md
pydcov/cmake/__init__.py
pydcov/cmake/coverage.cmake
pydcov/core/__init__.py
pydcov/core/coverage_manager.py
pydcov/core/incremental_coverage.py
pydcov/templates/__init__.py
pydcov/templates/basic_cpp/CMakeLists.txt
pydcov/templates/basic_cpp/README.md
pydcov/templates/basic_cpp/app/main.cpp
pydcov/templates/basic_cpp/src/CMakeLists.txt
pydcov/templates/basic_cpp/src/calculator.cpp
pydcov/templates/basic_cpp/src/calculator.hpp
pydcov/templates/basic_cpp/tests/CMakeLists.txt
pydcov/templates/basic_cpp/tests/test_calculator.cpp
pydcov/utils/__init__.py
pydcov/utils/cmake_integration.py
pydcov/utils/compiler_detection.py
pydcov/utils/logging_config.py
pydcov/utils/path_utils.py
pydcov/utils/test_executor.py